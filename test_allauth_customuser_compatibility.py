#!/usr/bin/env python
"""
Comprehensive test for CustomUser model compatibility with django-allauth.
This test verifies all enhanced features and allauth integration.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from allauth.account.utils import user_email, user_username
from allauth.account.models import EmailAddress

def test_enhanced_customuser_model():
    """Test all enhanced CustomUser model features and allauth compatibility."""
    
    print("🧪 Testing Enhanced CustomUser Model with Django Allauth")
    print("=" * 70)
    
    User = get_user_model()
    
    # Test 1: Basic Model Configuration
    print("\n1. Testing Model Configuration...")
    assert User.__name__ == 'CustomUser', f"Expected CustomUser, got {User.__name__}"
    assert User.USERNAME_FIELD == 'email', f"Expected email as USERNAME_FIELD"
    assert User.EMAIL_FIELD == 'email', f"Expected email as EMAIL_FIELD"
    assert User.REQUIRED_FIELDS == [], f"Expected empty REQUIRED_FIELDS"
    print("   ✅ Model configuration is correct")
    
    # Test 2: Enhanced Manager Methods
    print("\n2. Testing Enhanced Manager Methods...")
    manager = User.objects
    assert hasattr(manager, 'get_by_natural_key'), "Manager missing get_by_natural_key method"
    assert hasattr(manager, 'normalize_email'), "Manager missing normalize_email method"
    print("   ✅ Manager methods are available")
    
    # Test 3: User Creation with Email Normalization
    print("\n3. Testing User Creation with Email Normalization...")
    user = User.objects.create_user(
        email='<EMAIL>',
        password='testpass123',
        first_name='John',
        last_name='Doe'
    )
    assert user.email == '<EMAIL>', f"Email not normalized: {user.email}"
    assert user.role == User.CUSTOMER, f"Default role not set correctly"
    assert user.is_active == True, f"User should be active by default"
    print(f"   ✅ User created with normalized email: {user.email}")
    
    # Test 4: Enhanced User Methods
    print("\n4. Testing Enhanced User Methods...")
    assert user.get_username() == user.email, "get_username() should return email"
    assert user.get_full_name() == "John Doe", f"get_full_name() returned: {user.get_full_name()}"
    assert user.get_short_name() == "John", f"get_short_name() returned: {user.get_short_name()}"
    assert user.natural_key() == (user.email,), f"natural_key() returned: {user.natural_key()}"
    print("   ✅ Enhanced user methods work correctly")
    
    # Test 5: Allauth Utility Functions
    print("\n5. Testing Allauth Utility Functions...")
    assert user_email(user) == user.email, "user_email() should return user's email"
    # user_username returns None for email-based auth, which is correct
    assert user_username(user) is None, "user_username() should return None for email-based auth"
    print("   ✅ Allauth utility functions work correctly")
    
    # Test 6: Role Functionality
    print("\n6. Testing Role Functionality...")
    assert user.is_customer == True, "User should be customer by default"
    assert user.is_service_provider == False, "User should not be service provider"
    assert user.is_admin == False, "User should not be admin"
    assert user.has_role('customer') == True, "has_role('customer') should return True"
    assert user.get_role_display_name() == 'Customer', "Role display name should be 'Customer'"
    print("   ✅ Role functionality works correctly")
    
    # Test 7: Role Setting and Validation
    print("\n7. Testing Role Setting and Validation...")
    user.set_role('service_provider')
    assert user.role == 'service_provider', "Role should be updated"
    assert user.is_service_provider == True, "User should now be service provider"
    
    try:
        user.set_role('invalid_role')
        assert False, "Should have raised ValidationError for invalid role"
    except ValidationError:
        pass  # Expected
    print("   ✅ Role setting and validation work correctly")
    
    # Test 8: Superuser Creation
    print("\n8. Testing Superuser Creation...")
    admin_user = User.objects.create_superuser(
        email='<EMAIL>',
        password='adminpass123'
    )
    assert admin_user.is_staff == True, "Superuser should be staff"
    assert admin_user.is_superuser == True, "Superuser should have superuser flag"
    assert admin_user.role == 'admin', "Superuser should have admin role"
    assert admin_user.is_admin == True, "Superuser should be admin"
    print("   ✅ Superuser creation works correctly")
    
    # Test 9: Email Normalization in Save Method
    print("\n9. Testing Email Normalization in Save Method...")
    user.email = '<EMAIL>'
    user.save()
    assert user.email == '<EMAIL>', f"Email not normalized on save: {user.email}"
    print("   ✅ Email normalization in save method works correctly")
    
    # Test 10: Backward Compatibility Properties
    print("\n10. Testing Backward Compatibility Properties...")
    assert user.full_name == user.get_full_name(), "full_name property should match get_full_name()"
    assert user.short_name == user.get_short_name(), "short_name property should match get_short_name()"
    print("   ✅ Backward compatibility properties work correctly")
    
    # Test 11: Case-Insensitive Email Uniqueness
    print("\n11. Testing Case-Insensitive Email Uniqueness...")
    try:
        User.objects.create_user(
            email='<EMAIL>',  # Same as user.email but different case
            password='anotherpass'
        )
        assert False, "Should have raised IntegrityError for duplicate email"
    except IntegrityError:
        pass  # Expected
    print("   ✅ Case-insensitive email uniqueness works correctly")
    
    # Test 12: Natural Key Functionality
    print("\n12. Testing Natural Key Functionality...")
    retrieved_user = User.objects.get_by_natural_key(user.email)
    assert retrieved_user.id == user.id, "get_by_natural_key should return the same user"
    print("   ✅ Natural key functionality works correctly")
    
    # Test 13: Edge Cases for get_full_name and get_short_name
    print("\n13. Testing Edge Cases for Name Methods...")
    user_no_name = User(email='<EMAIL>')
    assert user_no_name.get_full_name() == '<EMAIL>', "get_full_name should fallback to email"
    assert user_no_name.get_short_name() == 'noname.test', "get_short_name should fallback to email prefix"
    print("   ✅ Name method edge cases work correctly")
    
    # Clean up
    user.delete()
    admin_user.delete()
    
    print("\n" + "=" * 70)
    print("🎉 ALL TESTS PASSED! Enhanced CustomUser model is fully allauth-compatible!")
    print("=" * 70)
    
    # Summary
    print("\n📋 Enhancement Summary:")
    print("   • Email normalization in manager and save method")
    print("   • Enhanced get_full_name() and get_short_name() with fallbacks")
    print("   • Natural key support for serialization")
    print("   • get_by_natural_key() manager method")
    print("   • Role validation and management methods")
    print("   • Backward compatibility properties")
    print("   • Full allauth compatibility")
    print("   • Case-insensitive email uniqueness")
    print("   • Improved superuser creation with admin role")
    
    return True

if __name__ == '__main__':
    try:
        test_enhanced_customuser_model()
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
